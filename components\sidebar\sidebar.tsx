"use client"
import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  BarChart3,
  Apple,
  Calendar,
  LogOut,
  Utensils
} from 'lucide-react';

interface SidebarProps {
  className?: string;
}

interface NavigationItem {
  href: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
}

const Sidebar: React.FC<SidebarProps> = ({ className = '' }) => {
  const pathname = usePathname();

  // TODO: Replace with actual user data from your backend/auth system
  const userData = {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '', // Add actual avatar URL when available
  };

  const navigationItems: NavigationItem[] = [
    {
      href: '/dashboard',
      label: 'Dashboard',
      icon: Bar<PERSON>hart3,
    },
    {
      href: '/product',
      label: 'Produkty',
      icon: Apple,
    },
    {
      href: '/meal-plan',
      label: 'Jadłospisy',
      icon: Calendar,
    },
  ];

  // TODO: Implement actual logout functionality
  const handleLogout = () => {
    console.log('Logout clicked - implement actual logout logic');
  };

  return (
    <div className={`hidden flex-col h-full w-64 bg-white border-r border-gray-200 ${className} lg:flex`}>
      {/* Header */}
      <div className="flex items-center gap-3 p-6 pb-4">
        <div className="flex items-center justify-center w-10 h-10 bg-primary rounded-lg">
          <Utensils className="w-5 h-5 text-white" />
        </div>
        <h1 className="text-xl font-semibold text-gray-900">Jadłospisy</h1>
      </div>

      <Separator  />

      {/* User Profile */}
      <div className="flex items-center gap-3 p-6 py-4">
        <Avatar className="w-10 h-10">
          <AvatarImage src={userData.avatar} alt={userData.name} />
          <AvatarFallback className="bg-gray-100 text-gray-600 text-sm font-medium">
            {userData.name.split(' ').map(n => n[0]).join('')}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col min-w-0 flex-1">
          <p className="text-sm font-medium text-gray-900 truncate">
            {userData.name}
          </p>
          <p className="text-xs text-gray-500 truncate">
            {userData.email}
          </p>
        </div>
      </div>

      <Separator/>

      {/* Navigation */}
      <nav className="flex-1 p-6 pt-4">
        <ul className="space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            const isActive = pathname === item.href;
            return (
              <li key={item.href}>
                <Button asChild variant={isActive ? "default" : "ghost"}>
                  <Link
                    href={item.href}
                    className='w-full'
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm font-medium">{item.label}</span>
                  </Link>
                </Button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Logout */}
      <div className="p-6 pt-0">
        <Button
          variant="ghost"
          className="w-full justify-start gap-3 h-10 px-3 text-gray-600 hover:text-gray-900 hover:bg-gray-50"
          onClick={handleLogout}
        >
          <LogOut className="w-4 h-4" />
          <span className="text-sm font-medium">Wyloguj</span>
        </Button>
      </div>
    </div>
  );
};

export default Sidebar;